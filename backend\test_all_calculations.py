"""
Comprehensive test script to verify all calculation fixes and data integrity.
This script tests delta calculations, KvK performance metrics, and display logic.
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from sqlalchemy.orm import Session
from backend.database import SessionLocal
from backend import crud, services
import json
from datetime import datetime

def test_delta_calculations():
    """Test delta calculation logic with edge cases."""
    print("=" * 60)
    print("TESTING DELTA CALCULATIONS")
    print("=" * 60)

    db = SessionLocal()
    try:
        # Get all scans
        scans = crud.get_scans(db)
        if len(scans) < 2:
            print("❌ Need at least 2 scans to test delta calculations")
            return False

        # Get baseline and latest scan
        baseline_scan = crud.get_baseline_scan(db)
        latest_scan = crud.get_latest_scan(db)

        if not baseline_scan or not latest_scan:
            print("❌ Missing baseline or latest scan")
            return False

        print(f"✅ Testing delta calculation between:")
        print(f"   Baseline: {baseline_scan.name} (ID: {baseline_scan.id})")
        print(f"   Latest: {latest_scan.name} (ID: {latest_scan.id})")

        # Calculate delta stats
        delta_stats = services.calculate_delta_stats(db, latest_scan, baseline_scan)

        print(f"✅ Calculated {len(delta_stats)} delta stats")

        # Check for data integrity issues
        issues = []
        warnings = []
        negative_kp_count = 0
        zero_kp_count = 0
        high_performers = 0
        underperformers = 0

        for ds in delta_stats:
            # Check for negative KP delta (treat as warning, not error)
            if ds.kill_points_delta < 0:
                negative_kp_count += 1
                warnings.append(f"Player {ds.player.name if ds.player else 'Unknown'}: Negative KP delta = {ds.kill_points_delta}")

            # Count zero KP delta
            if ds.kill_points_delta == 0:
                zero_kp_count += 1

            # Count high performers (100M+ KP)
            if ds.kill_points_delta >= 100_000_000:
                high_performers += 1

            # Count underperformers (less than 10M KP, excluding new players)
            if ds.kill_points_delta < 10_000_000 and not getattr(ds, 'is_new_player', False):
                underperformers += 1

            # Verify KP per dead calculation
            if ds.dead_troops_delta > 0 and ds.kill_points_delta > 0:
                expected_kp_per_dead = ds.kill_points_delta / ds.dead_troops_delta
                if abs(ds.kp_per_dead - expected_kp_per_dead) > 0.01:
                    issues.append(f"Player {ds.player.name if ds.player else 'Unknown'}: KP per dead mismatch")

        print(f"\n📊 Delta Stats Summary:")
        print(f"   Total players: {len(delta_stats)}")
        print(f"   Negative KP deltas: {negative_kp_count}")
        print(f"   Zero KP deltas: {zero_kp_count}")
        print(f"   High performers (100M+ KP): {high_performers}")
        print(f"   Underperformers (<10M KP): {underperformers}")

        if warnings:
            print(f"\n⚠️  Found {len(warnings)} data quality warnings:")
            for warning in warnings[:5]:  # Show first 5 warnings
                print(f"   - {warning}")
            if len(warnings) > 5:
                print(f"   ... and {len(warnings) - 5} more warnings")

        if issues:
            print(f"\n❌ Found {len(issues)} calculation errors:")
            for issue in issues[:5]:  # Show first 5 issues
                print(f"   - {issue}")
            if len(issues) > 5:
                print(f"   ... and {len(issues) - 5} more errors")

        # Only fail if there are actual calculation errors, not data quality warnings
        return len(issues) == 0

    except Exception as e:
        print(f"❌ Error testing delta calculations: {str(e)}")
        import traceback
        traceback.print_exc()
        return False
    finally:
        db.close()

def test_kvk_performance_calculations():
    """Test KvK performance calculation logic."""
    print("\n" + "=" * 60)
    print("TESTING KVK PERFORMANCE CALCULATIONS")
    print("=" * 60)

    db = SessionLocal()
    try:
        # Get active KvK
        kvks = crud.get_kvks(db)
        active_kvk = next((k for k in kvks if k.status == "active"), None)

        if not active_kvk:
            print("❌ No active KvK found")
            return False

        print(f"✅ Testing KvK: {active_kvk.name}")

        # Get performance summary
        performance_data = services.get_kvk_performance_summary(db, active_kvk.id, limit=10000)

        if not performance_data.get('performance_data'):
            print("❌ No performance data found")
            return False

        players = performance_data['performance_data']
        print(f"✅ Analyzing {len(players)} players")

        # Verify calculations
        issues = []

        for player in players[:10]:  # Check first 10 players in detail
            # Verify T4+T5 calculation
            expected_t45 = (player.get('t4_delta', 0) + player.get('t5_delta', 0))
            if player.get('t45_delta', 0) != expected_t45:
                issues.append(f"Player {player['player_name']}: T45 calculation mismatch")

            # Verify efficiency score logic
            kp_delta = player.get('kp_delta', 0)
            power_delta = player.get('power_delta', 0)
            efficiency_score = player.get('efficiency_score', 0)

            # Check efficiency calculation based on our new logic
            if power_delta < 0 and kp_delta > 0:
                expected_efficiency = kp_delta / abs(power_delta)
                if abs(efficiency_score - expected_efficiency) > 0.01:
                    issues.append(f"Player {player['player_name']}: Efficiency score mismatch for power loss scenario")

            # Verify underperforming flag
            needs_improvement = player.get('needs_improvement', False)
            is_new_player = player.get('is_new_player', False)

            expected_needs_improvement = (
                not is_new_player and
                kp_delta < 10_000_000
            )

            if needs_improvement != expected_needs_improvement:
                issues.append(f"Player {player['player_name']}: Underperforming flag mismatch")

        # Summary stats verification
        summary = performance_data.get('summary_stats', {})
        print(f"\n📊 KvK Summary Stats:")
        print(f"   Total players: {summary.get('total_players', 0)}")
        print(f"   Total KP gain: {summary.get('total_kp_gain', 0):,}")
        print(f"   Total power gain: {summary.get('total_power_gain', 0):,}")
        print(f"   Total power loss: {summary.get('total_power_loss', 0):,}")
        print(f"   Net power change: {summary.get('net_power_change', 0):,}")
        print(f"   Top performers: {summary.get('top_performers', 0)}")
        print(f"   Needs improvement: {summary.get('needs_improvement', 0)}")

        if issues:
            print(f"\n⚠️  Found {len(issues)} calculation issues:")
            for issue in issues[:5]:
                print(f"   - {issue}")
        else:
            print("\n✅ All KvK calculations are correct")

        return len(issues) == 0

    except Exception as e:
        print(f"❌ Error testing KvK performance: {str(e)}")
        import traceback
        traceback.print_exc()
        return False
    finally:
        db.close()

def test_display_data_mapping():
    """Test that data is correctly mapped for frontend display."""
    print("\n" + "=" * 60)
    print("TESTING DISPLAY DATA MAPPING")
    print("=" * 60)

    db = SessionLocal()
    try:
        # Test dashboard data
        dashboard_data = crud.get_dashboard_data(db)

        print("✅ Dashboard Data:")
        print(f"   Total power: {dashboard_data.get('total_power', 0):,}")
        print(f"   Total KP: {dashboard_data.get('total_kill_points', 0):,}")
        print(f"   Power gain: {dashboard_data.get('power_gain', 0):,}")
        print(f"   KP gain: {dashboard_data.get('kill_points_gain', 0):,}")

        # Verify gains are positive values only
        if dashboard_data.get('power_gain', 0) < 0:
            print("❌ Power gain should not be negative in dashboard")
            return False
        if dashboard_data.get('kill_points_gain', 0) < 0:
            print("❌ KP gain should not be negative in dashboard")
            return False

        # Test general performance data
        performance_data = services.get_general_performance_summary(db, limit=10)

        if performance_data.get('performance_data'):
            print(f"\n✅ General Performance Data ({len(performance_data['performance_data'])} players)")

            # Check first player's data structure
            if performance_data['performance_data']:
                player = performance_data['performance_data'][0]
                required_fields = [
                    'player_id', 'player_name', 'governor_id', 'alliance',
                    'current_power', 'current_kp', 'current_dead',
                    'power_delta', 'kp_delta', 'dead_delta',
                    't4_delta', 't5_delta', 't45_delta',
                    'kp_per_dead', 'is_top_performer', 'needs_improvement'
                ]

                missing_fields = [f for f in required_fields if f not in player]
                if missing_fields:
                    print(f"❌ Missing fields in player data: {missing_fields}")
                    return False
                else:
                    print("✅ All required fields present in player data")

        return True

    except Exception as e:
        print(f"❌ Error testing display data: {str(e)}")
        import traceback
        traceback.print_exc()
        return False
    finally:
        db.close()

def test_excel_data_integrity():
    """Test that Excel data is being processed correctly."""
    print("\n" + "=" * 60)
    print("TESTING EXCEL DATA INTEGRITY")
    print("=" * 60)

    db = SessionLocal()
    try:
        # Get latest scan
        latest_scan = crud.get_latest_scan(db)
        if not latest_scan:
            print("❌ No scans found")
            return False

        print(f"✅ Checking latest scan: {latest_scan.name}")

        # Check for common data issues
        issues = []

        for stat in latest_scan.player_stats[:20]:  # Check first 20 players
            player = stat.player

            # Check for negative values that shouldn't be negative
            if stat.power and stat.power < 0:
                issues.append(f"Player {player.name}: Negative power = {stat.power}")

            if stat.total_kill_points and stat.total_kill_points < 0:
                issues.append(f"Player {player.name}: Negative total KP = {stat.total_kill_points}")

            if stat.dead_troops and stat.dead_troops < 0:
                issues.append(f"Player {player.name}: Negative dead troops = {stat.dead_troops}")

            # Check T4+T5 calculation
            t4 = stat.kill_points_t4 or 0
            t5 = stat.kill_points_t5 or 0
            t45 = stat.t45_kills or 0

            if t45 != (t4 + t5) and t45 != 0:  # Allow 0 if not calculated
                issues.append(f"Player {player.name}: T45 mismatch: {t45} != {t4} + {t5}")

        if issues:
            print(f"⚠️  Found {len(issues)} data integrity issues:")
            for issue in issues[:5]:
                print(f"   - {issue}")
        else:
            print("✅ No data integrity issues found")

        return len(issues) == 0

    except Exception as e:
        print(f"❌ Error testing Excel data: {str(e)}")
        import traceback
        traceback.print_exc()
        return False
    finally:
        db.close()

def main():
    """Run all calculation tests."""
    print("RISE OF KINGDOMS - COMPREHENSIVE CALCULATION VERIFICATION")
    print("=" * 60)
    print(f"Testing started at: {datetime.now()}")
    print("=" * 60)

    tests = [
        ("Excel Data Integrity", test_excel_data_integrity),
        ("Delta Calculations", test_delta_calculations),
        ("KvK Performance Calculations", test_kvk_performance_calculations),
        ("Display Data Mapping", test_display_data_mapping)
    ]

    results = []
    for test_name, test_func in tests:
        print(f"\n🔍 Running: {test_name}")
        try:
            success = test_func()
            results.append((test_name, success))
        except Exception as e:
            print(f"❌ Test crashed: {str(e)}")
            results.append((test_name, False))

    # Summary
    print("\n" + "=" * 60)
    print("TEST SUMMARY")
    print("=" * 60)

    passed = sum(1 for _, success in results if success)
    total = len(results)

    for test_name, success in results:
        status = "✅ PASSED" if success else "❌ FAILED"
        print(f"{test_name}: {status}")

    print(f"\nTotal: {passed}/{total} tests passed")

    if passed == total:
        print("\n🎉 All calculations are working correctly!")
        print("✅ You can restart the frontend and backend servers now.")
        return True
    else:
        print("\n⚠️  Some calculations still have issues. Please review the failed tests.")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)