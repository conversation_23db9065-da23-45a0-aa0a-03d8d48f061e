"""
Create database tables using the recreate_tables function.
"""

import sys
import os

# Add the current directory to the path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

try:
    print("Creating database tables...")
    from database import recreate_tables, SessionLocal
    import crud
    
    # Create all tables
    recreate_tables()
    print("✅ Database tables created successfully")
    
    # Initialize default parameters
    print("Initializing default parameters...")
    db = SessionLocal()
    try:
        crud.initialize_default_parameters(db)
        print("✅ Default parameters initialized")
    finally:
        db.close()
    
    print("\n🎉 Database setup completed successfully!")
    print("You can now run the calculation tests.")
    
except Exception as e:
    print(f"❌ Error creating tables: {e}")
    import traceback
    traceback.print_exc()
