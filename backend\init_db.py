"""
Initialize the database with all required tables.
"""

import sys
import os

# Add the parent directory to the path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

try:
    print("Initializing database...")
    from backend.database import engine, Base, SessionLocal
    from backend import models, crud
    
    # Create all tables
    print("Creating database tables...")
    Base.metadata.create_all(bind=engine)
    print("✅ Database tables created successfully")
    
    # Initialize default parameters
    print("Initializing default parameters...")
    db = SessionLocal()
    try:
        crud.initialize_default_parameters(db)
        print("✅ Default parameters initialized")
    finally:
        db.close()
    
    print("\n🎉 Database initialization completed successfully!")
    print("You can now run the calculation tests.")
    
except Exception as e:
    print(f"❌ Error initializing database: {e}")
    import traceback
    traceback.print_exc()
