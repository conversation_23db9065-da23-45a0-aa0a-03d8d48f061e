from sqlalchemy.orm import Session
import crud, crud_kvk, models, schemas
from typing import List, Optional, Dict, Any
import pandas as pd
import io
import openpyxl
import xlrd
from fastapi import HTTPException
from cache import cache_scan_data, cache_player_data, cache_kvk_data, cache_reports, invalidate_scan_cache, invalidate_player_cache, invalidate_kvk_cache, invalidate_reports_cache
from logging_config import get_logger

logger = get_logger("services")

# --- File Parsing Functions ---
def parse_player_data_file(file_content: bytes, filename: str) -> List[schemas.ScanDataItem]:
    """Parse Excel or CSV file containing player scan data."""
    try:
        # Determine file type
        if filename.lower().endswith('.csv'):
            # Parse CSV file
            df = pd.read_csv(io.BytesIO(file_content))
        elif filename.lower().endswith(('.xlsx', '.xls')):
            # Parse Excel file
            df = pd.read_excel(io.BytesIO(file_content), engine='openpyxl' if filename.lower().endswith('.xlsx') else 'xlrd')
        else:
            raise HTTPException(status_code=400, detail="Unsupported file format. Please upload CSV or Excel files.")

        # Normalize column names (convert to lowercase and replace spaces/special chars)
        df.columns = df.columns.str.lower().str.replace(' ', '_').str.replace('-', '_')

        # Define column mappings for flexible parsing
        column_mappings = {
            'player_name': ['name', 'player_name', 'governor_name', 'player'],
            'governor_id': ['governor_id', 'gov_id', 'id', 'player_id'],
            'alliance': ['alliance', 'alliance_tag', 'ally'],
            'power': ['power', 'total_power'],
            'total_kill_points': ['total_kill_points', 'kill_points', 'kp', 'total_kp', 'killpoints'],
            'dead_troops': ['dead_troops', 'deads', 'dead', 'total_dead'],
            'kill_points_t1': ['t1_kills', 't1_kill', 'tier_1_kills', 'kill_points_t1'],
            'kill_points_t2': ['t2_kills', 't2_kill', 'tier_2_kills', 'kill_points_t2'],
            'kill_points_t3': ['t3_kills', 't3_kill', 'tier_3_kills', 'kill_points_t3'],
            'kill_points_t4': ['t4_kills', 't4_kill', 'tier_4_kills', 'kill_points_t4'],
            'kill_points_t5': ['t5_kills', 't5_kill', 'tier_5_kills', 'kill_points_t5'],
            't45_kills': ['t45_kills', 't4_5_kills', 't4+t5_kills'],
            'ranged': ['ranged', 'ranged_points'],
            'rss_gathered': ['rss_gathered', 'rss_gather', 'resource_gathered'],
            'rss_assistance': ['rss_assistance', 'rss_assist', 'resource_assistance'],
            'helps': ['helps', 'help_count']
        }

        # Map columns to standardized names (case-insensitive)
        mapped_columns = {}
        df_columns_lower = [col.lower() for col in df.columns]

        for standard_name, possible_names in column_mappings.items():
            for possible_name in possible_names:
                if possible_name.lower() in df_columns_lower:
                    # Find the actual column name (with original case)
                    actual_col = df.columns[df_columns_lower.index(possible_name.lower())]
                    mapped_columns[standard_name] = actual_col
                    break

        # Check for required columns
        required_columns = ['player_name', 'power', 'total_kill_points', 'dead_troops']
        missing_columns = []
        for req_col in required_columns:
            if req_col not in mapped_columns:
                missing_columns.append(req_col)

        if missing_columns:
            raise HTTPException(
                status_code=400,
                detail=f"Missing required columns: {', '.join(missing_columns)}. Available columns: {', '.join(df.columns.tolist())}"
            )

        # Parse data
        parsed_data = []
        for index, row in df.iterrows():
            try:
                # Extract required fields
                player_name = str(row[mapped_columns['player_name']]).strip()
                if not player_name or player_name.lower() in ['nan', 'none', '']:
                    continue  # Skip rows without player names

                power = int(float(row[mapped_columns['power']]) if pd.notna(row[mapped_columns['power']]) else 0)
                total_kill_points = int(float(row[mapped_columns['total_kill_points']]) if pd.notna(row[mapped_columns['total_kill_points']]) else 0)
                dead_troops = int(float(row[mapped_columns['dead_troops']]) if pd.notna(row[mapped_columns['dead_troops']]) else 0)

                # Extract optional fields
                # Convert governor_id to integer first to handle both float and int values from Excel, then to string
                if 'governor_id' in mapped_columns and pd.notna(row[mapped_columns['governor_id']]):
                    try:
                        governor_id = str(int(float(row[mapped_columns['governor_id']])))
                    except (ValueError, TypeError):
                        governor_id = str(row[mapped_columns['governor_id']]).strip()
                else:
                    governor_id = None

                alliance = str(row[mapped_columns['alliance']]).strip() if 'alliance' in mapped_columns and pd.notna(row[mapped_columns['alliance']]) else None

                # Extract tier kill points
                kill_points_t1 = int(float(row[mapped_columns['kill_points_t1']]) if 'kill_points_t1' in mapped_columns and pd.notna(row[mapped_columns['kill_points_t1']]) else 0)
                kill_points_t2 = int(float(row[mapped_columns['kill_points_t2']]) if 'kill_points_t2' in mapped_columns and pd.notna(row[mapped_columns['kill_points_t2']]) else 0)
                kill_points_t3 = int(float(row[mapped_columns['kill_points_t3']]) if 'kill_points_t3' in mapped_columns and pd.notna(row[mapped_columns['kill_points_t3']]) else 0)
                kill_points_t4 = int(float(row[mapped_columns['kill_points_t4']]) if 'kill_points_t4' in mapped_columns and pd.notna(row[mapped_columns['kill_points_t4']]) else 0)
                kill_points_t5 = int(float(row[mapped_columns['kill_points_t5']]) if 'kill_points_t5' in mapped_columns and pd.notna(row[mapped_columns['kill_points_t5']]) else 0)

                # Calculate t45_kills if not provided
                t45_kills = int(float(row[mapped_columns['t45_kills']]) if 't45_kills' in mapped_columns and pd.notna(row[mapped_columns['t45_kills']]) else kill_points_t4 + kill_points_t5)

                # Extract other optional fields
                ranged = int(float(row[mapped_columns['ranged']]) if 'ranged' in mapped_columns and pd.notna(row[mapped_columns['ranged']]) else 0)
                rss_gathered = int(float(row[mapped_columns['rss_gathered']]) if 'rss_gathered' in mapped_columns and pd.notna(row[mapped_columns['rss_gathered']]) else 0)
                rss_assistance = int(float(row[mapped_columns['rss_assistance']]) if 'rss_assistance' in mapped_columns and pd.notna(row[mapped_columns['rss_assistance']]) else 0)
                helps = int(float(row[mapped_columns['helps']]) if 'helps' in mapped_columns and pd.notna(row[mapped_columns['helps']]) else 0)

                # Clean up governor_id and alliance
                if governor_id and governor_id.lower() in ['nan', 'none', '']:
                    governor_id = None
                if alliance and alliance.lower() in ['nan', 'none', '']:
                    alliance = None

                scan_data_item = schemas.ScanDataItem(
                    player_name=player_name,
                    power=power,
                    total_kill_points=total_kill_points,
                    dead_troops=dead_troops,
                    alliance=alliance,
                    governor_id=governor_id,
                    kill_points_t1=kill_points_t1,
                    kill_points_t2=kill_points_t2,
                    kill_points_t3=kill_points_t3,
                    kill_points_t4=kill_points_t4,
                    kill_points_t5=kill_points_t5,
                    t45_kills=t45_kills,
                    ranged=ranged,
                    rss_gathered=rss_gathered,
                    rss_assistance=rss_assistance,
                    helps=helps
                )
                parsed_data.append(scan_data_item)

            except Exception as e:
                print(f"Error parsing row {index}: {e}")
                continue  # Skip problematic rows

        if not parsed_data:
            raise HTTPException(status_code=400, detail="No valid player data found in the file.")

        print(f"Successfully parsed {len(parsed_data)} players from {filename}")
        return parsed_data

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=400, detail=f"Error parsing file: {str(e)}")

# --- Player and Scan Management ---
def get_or_create_player(db: Session, name: str, governor_id: Optional[str], alliance: Optional[str]) -> models.Player:
    """
    Gets an existing player or creates a new one.
    STRICTLY prioritizes governor_id for matching since players can change names.
    Only falls back to name matching if governor_id is missing AND no conflicts exist.
    """
    db_player = None

    # PRIORITY 1: Match by governor_id (MOST RELIABLE - players can change names but ID stays same)
    if governor_id:
        db_player = crud.get_player_by_gov_id(db, governor_id=governor_id)
        if db_player:
            # Update name if it has changed (common in KvK when players change names)
            if name and db_player.name != name:
                print(f"Player name changed: {db_player.name} -> {name} (ID: {governor_id})")
                db_player.name = name

            # Update alliance if provided and different
            if alliance and db_player.alliance != alliance:
                print(f"Player alliance changed: {db_player.alliance} -> {alliance} for {name}")
                db_player.alliance = alliance

            db.commit()
            db.refresh(db_player)
            return db_player

    # PRIORITY 2: Only use name matching if governor_id is missing AND it's safe to do so
    if name and not governor_id:
        # Check if there are multiple players with this name (dangerous for matching)
        all_players_with_name = db.query(models.Player).filter(models.Player.name == name).all()

        if len(all_players_with_name) == 1:
            # Safe to match by name since there's only one player with this name
            db_player = all_players_with_name[0]
            print(f"Matched player by name (safe - unique name): {name}")

            # Update alliance if provided and different
            if alliance and db_player.alliance != alliance:
                db_player.alliance = alliance
                db.commit()
                db.refresh(db_player)

            return db_player
        elif len(all_players_with_name) > 1:
            # DANGEROUS: Multiple players with same name - cannot safely match without governor_id
            print(f"WARNING: Multiple players found with name '{name}' but no governor_id provided. Cannot safely match.")
            print(f"Found players: {[f'{p.name} (ID: {p.governor_id})' for p in all_players_with_name]}")
            # Fall through to create new player with warning name

    # Create new player if not found or unsafe to match
    if not name:
        raise HTTPException(status_code=400, detail=f"Player name is required to create a new player if governor_id ('{governor_id}') is not found.")

    # Handle duplicate names by making them unique
    existing_player_with_name = crud.get_player_by_name(db, name=name)
    if existing_player_with_name and existing_player_with_name.governor_id != governor_id:
        # There's already a player with this name but different governor_id
        # This means we have duplicate names - append governor_id to make it unique
        if governor_id:
            unique_name = f"{name} (ID: {governor_id})"
        else:
            # If no governor_id, use a timestamp or counter to make it unique
            import time
            unique_name = f"{name} (No ID - {int(time.time())})"

        print(f"Duplicate name detected: '{name}'. Creating player with unique name: '{unique_name}'")
        name = unique_name

    print(f"Creating new player: {name} (ID: {governor_id}, Alliance: {alliance})")
    return crud.create_player(db, schemas.PlayerCreate(name=name, governor_id=governor_id, alliance=alliance))

def process_scan_data(db: Session, scan_upload: schemas.ScanUpload) -> models.Scan:
    """Processes uploaded scan data, creates/updates players, and records player stats."""
    logger.info(f"Processing scan data: {scan_upload.scan_name}")

    # 1. Create or get the Scan record
    db_scan = crud.get_scan_by_name(db, name=scan_upload.scan_name)
    if db_scan and not scan_upload.is_baseline: # Allow re-uploading baseline if specified
        # Potentially add logic here if re-uploading a non-baseline scan should be allowed/denied or update existing
        raise HTTPException(status_code=400, detail=f"Scan with name '{scan_upload.scan_name}' already exists.")
    elif db_scan and scan_upload.is_baseline and not db_scan.is_baseline:
        # If existing scan is not baseline, but new upload says it is, update it.
        db_scan.is_baseline = True
        db.commit()
        db.refresh(db_scan)
    elif not db_scan:
        db_scan = crud.create_scan(db, schemas.ScanCreate(
            name=scan_upload.scan_name,
            is_baseline=scan_upload.is_baseline,
            kvk_id=scan_upload.kvk_id
        ))

    # If this scan is marked as baseline, ensure no other scan in the same KvK is baseline
    if db_scan.is_baseline and db_scan.kvk_id:
        other_baseline_scans = db.query(models.Scan).filter(
            models.Scan.is_baseline == True,
            models.Scan.kvk_id == db_scan.kvk_id,
            models.Scan.id != db_scan.id
        ).all()
        for obs in other_baseline_scans:
            obs.is_baseline = False
        db.commit()
    elif db_scan.is_baseline and not db_scan.kvk_id:
        # For scans not associated with a KvK, keep the global baseline logic
        other_baseline_scans = db.query(models.Scan).filter(
            models.Scan.is_baseline == True,
            models.Scan.kvk_id.is_(None),
            models.Scan.id != db_scan.id
        ).all()
        for obs in other_baseline_scans:
            obs.is_baseline = False
        db.commit()

    # 2. Process each player's data in the scan
    for item in scan_upload.data:
        player = get_or_create_player(db, name=item.player_name, governor_id=item.governor_id, alliance=item.alliance)

        # Check if stat for this player and scan already exists (e.g. if re-processing)
        existing_stat = crud.get_player_stat(db, player_id=player.id, scan_id=db_scan.id)
        if existing_stat:
            # Update existing stat
            existing_stat.power = item.power
            existing_stat.kill_points_t1 = item.kill_points_t1 or 0
            existing_stat.kill_points_t2 = item.kill_points_t2 or 0
            existing_stat.kill_points_t3 = item.kill_points_t3 or 0
            existing_stat.kill_points_t4 = item.kill_points_t4 or 0
            existing_stat.kill_points_t5 = item.kill_points_t5 or 0
            existing_stat.total_kill_points = item.total_kill_points
            existing_stat.dead_troops = item.dead_troops
            existing_stat.t45_kills = item.t45_kills or 0
            existing_stat.ranged = item.ranged or 0
            existing_stat.rss_gathered = item.rss_gathered or 0
            existing_stat.rss_assistance = item.rss_assistance or 0
            existing_stat.helps = item.helps or 0
            db.commit()
        else:
            # Create new player stat
            player_stat_create = schemas.PlayerStatCreate(
                power=item.power,
                kill_points_t1=item.kill_points_t1 or 0,
                kill_points_t2=item.kill_points_t2 or 0,
                kill_points_t3=item.kill_points_t3 or 0,
                kill_points_t4 = item.kill_points_t4 or 0,
                kill_points_t5 = item.kill_points_t5 or 0,
                total_kill_points = item.total_kill_points,
                dead_troops = item.dead_troops,
                t45_kills=item.t45_kills or 0,
                ranged=item.ranged or 0,
                rss_gathered=item.rss_gathered or 0,
                rss_assistance=item.rss_assistance or 0,
                helps=item.helps or 0
            )
            crud.create_player_stat(db, player_stat=player_stat_create, player_id=player.id, scan_id=db_scan.id)

    # 3. After processing all players for the current scan, calculate delta stats
    #    against the baseline scan or the immediately previous scan.
    #    For simplicity, let's calculate against the official baseline scan if it exists.
    baseline_scan = crud.get_baseline_scan(db)
    if baseline_scan and baseline_scan.id != db_scan.id: # Don't compare a scan to itself
        calculate_delta_stats_for_scan_pair(db, start_scan_id=baseline_scan.id, end_scan_id=db_scan.id)

    # Invalidate relevant caches after processing scan data
    logger.info("Invalidating caches after scan processing")
    invalidate_scan_cache()
    invalidate_player_cache()
    invalidate_reports_cache()
    if scan_upload.kvk_id:
        invalidate_kvk_cache()

    logger.info(f"Successfully processed scan: {scan_upload.scan_name}")
    return db_scan

# --- Player ID Validation Functions ---
def validate_player_id_consistency(db: Session, scan_id: int) -> Dict:
    """Validate player ID consistency and detect potential data issues."""
    scan = crud.get_scan(db, scan_id=scan_id)
    if not scan:
        raise HTTPException(status_code=404, detail="Scan not found")

    validation_results = {
        "scan_id": scan_id,
        "total_players": len(scan.player_stats),
        "validation_issues": [],
        "duplicate_ids": [],
        "missing_ids": [],
        "name_changes": [],
        "valid_players": 0
    }

    # Check for duplicate player IDs in the same scan
    player_ids = [stat.player_id for stat in scan.player_stats if stat.player_id]
    duplicate_ids = [pid for pid in set(player_ids) if player_ids.count(pid) > 1]

    if duplicate_ids:
        validation_results["duplicate_ids"] = duplicate_ids
        validation_results["validation_issues"].append(f"Found {len(duplicate_ids)} duplicate player IDs")

    # Check for missing player IDs
    missing_ids = [stat for stat in scan.player_stats if not stat.player_id]
    if missing_ids:
        validation_results["missing_ids"] = [{"stat_id": stat.id, "player_name": stat.player.name if stat.player else "Unknown"} for stat in missing_ids]
        validation_results["validation_issues"].append(f"Found {len(missing_ids)} stats with missing player IDs")

    # Check for name changes by comparing with previous scans
    previous_scans = crud.get_recent_scans(db, limit=5)
    if len(previous_scans) > 1:
        for prev_scan in previous_scans[1:]:  # Skip current scan
            for current_stat in scan.player_stats:
                if not current_stat.player_id:
                    continue

                # Find same player in previous scan
                prev_stat = next((s for s in prev_scan.player_stats if s.player_id == current_stat.player_id), None)
                if prev_stat and prev_stat.player and current_stat.player:
                    if prev_stat.player.name != current_stat.player.name:
                        validation_results["name_changes"].append({
                            "player_id": current_stat.player_id,
                            "old_name": prev_stat.player.name,
                            "new_name": current_stat.player.name,
                            "previous_scan_id": prev_scan.id
                        })

    validation_results["valid_players"] = len([stat for stat in scan.player_stats if stat.player_id and stat.player_id not in duplicate_ids])

    return validation_results

def get_player_id_mapping(db: Session, scan_id: int) -> Dict:
    """Get mapping of player IDs to names and governor IDs for validation."""
    scan = crud.get_scan(db, scan_id=scan_id)
    if not scan:
        raise HTTPException(status_code=404, detail="Scan not found")

    mapping = {}
    for stat in scan.player_stats:
        if stat.player_id and stat.player:
            mapping[stat.player_id] = {
                "name": stat.player.name,
                "governor_id": stat.player.governor_id,
                "alliance": stat.player.alliance,
                "power": stat.power,
                "kill_points": stat.total_kill_points
            }

    return {
        "scan_id": scan_id,
        "scan_name": scan.name,
        "player_mapping": mapping,
        "total_mapped_players": len(mapping)
    }

# --- Delta Calculation and Performance Logic ---
def calculate_delta_stats(db: Session, current_scan: models.Scan, baseline_scan: models.Scan) -> List[models.DeltaStat]:
    """Calculate comprehensive delta stats between two scans for KvK gain analysis."""
    # Use governor_id for player matching instead of database player_id
    current_stats = {}
    baseline_stats = {}

    # Build current stats mapping by governor_id
    for stat in current_scan.player_stats:
        if stat.player and stat.player.governor_id:
            current_stats[stat.player.governor_id] = stat

    # Build baseline stats mapping by governor_id
    for stat in baseline_scan.player_stats:
        if stat.player and stat.player.governor_id:
            baseline_stats[stat.player.governor_id] = stat

    delta_stats = []
    players_processed = 0

    print(f"Calculating deltas between scan {current_scan.id} ({len(current_stats)} players) and baseline {baseline_scan.id} ({len(baseline_stats)} players)")

    # Calculate deltas for players in current scan
    for governor_id, current_stat in current_stats.items():
        baseline_stat = baseline_stats.get(governor_id)
        if baseline_stat:
            # Calculate all delta values with proper null handling
            power_delta = (current_stat.power or 0) - (baseline_stat.power or 0)
            kp_delta = (current_stat.total_kill_points or 0) - (baseline_stat.total_kill_points or 0)
            dead_delta = (current_stat.dead_troops or 0) - (baseline_stat.dead_troops or 0)

            # T4/T5 deltas - handle None values properly and allow negative values for losses
            current_t4 = current_stat.kill_points_t4 or 0
            baseline_t4 = baseline_stat.kill_points_t4 or 0
            current_t5 = current_stat.kill_points_t5 or 0
            baseline_t5 = baseline_stat.kill_points_t5 or 0

            t4_delta = current_t4 - baseline_t4
            t5_delta = current_t5 - baseline_t5

            # Kill points should never decrease in RoK (they only go up)
            # If we detect a negative delta, it likely means:
            # 1. Data entry error in Excel
            # 2. Player data was reset/corrected
            # 3. Different player with same governor ID
            if kp_delta < 0:
                print(f"WARNING: Negative KP delta detected for player {governor_id}: {kp_delta}")
                print(f"  Current KP: {current_stat.total_kill_points}, Baseline KP: {baseline_stat.total_kill_points}")
                print(f"  Player: {current_stat.player.name if current_stat.player else 'Unknown'}")
                # Don't set to 0 - preserve the actual value for investigation
                # The frontend should handle this appropriately

            # Calculate efficiency metrics
            # Only calculate KP per dead if both KP and dead troops increased
            if kp_delta > 0 and dead_delta > 0:
                kp_per_dead = kp_delta / dead_delta
            else:
                kp_per_dead = 0.0

            # Determine if player was zeroed (80%+ power loss from baseline)
            baseline_power = baseline_stat.power or 0
            if baseline_power > 0:
                power_loss_percentage = abs(power_delta) / baseline_power
                is_zeroed = power_loss_percentage >= 0.8 and power_delta < 0  # 80%+ power loss
            else:
                is_zeroed = False

            delta = models.DeltaStat(
                end_scan_id=current_scan.id,
                start_scan_id=baseline_scan.id,
                player_id=current_stat.player_id,  # Use the database player_id for foreign key
                power_delta=power_delta,
                kill_points_delta=kp_delta,
                dead_troops_delta=dead_delta,
                kp_per_dead=kp_per_dead,
                is_zeroed=is_zeroed
            )
            delta_stats.append(delta)
            players_processed += 1
        else:
            # Player exists in current but not baseline - new player joined kingdom
            print(f"New player joined kingdom: {current_stat.player.name if current_stat.player else 'Unknown'} (Gov ID: {governor_id})")

            # New players should not have delta calculations since they didn't "gain" these stats
            # They just joined with their existing stats
            delta = models.DeltaStat(
                end_scan_id=current_scan.id,
                start_scan_id=baseline_scan.id,
                player_id=current_stat.player_id,  # Use database player_id for foreign key
                power_delta=0,  # No delta for new players - they didn't gain this power
                kill_points_delta=0,  # No delta for new players - they didn't gain these KP
                dead_troops_delta=0,  # No delta for new players - they didn't gain these dead troops
                kp_per_dead=0.0,  # No efficiency calculation for new players
                is_new_player=True,  # Mark as new player
                is_zeroed=False
            )
            delta_stats.append(delta)
            players_processed += 1

    # Check for players who left the kingdom (in baseline but not in current)
    for governor_id, baseline_stat in baseline_stats.items():
        if governor_id not in current_stats:
            print(f"Player left kingdom: {baseline_stat.player.name if baseline_stat.player else 'Unknown'} (Gov ID: {governor_id})")
            # Create a negative delta to show the loss
            baseline_power = baseline_stat.power or 0
            delta = models.DeltaStat(
                end_scan_id=current_scan.id,
                start_scan_id=baseline_scan.id,
                player_id=baseline_stat.player_id,  # Use database player_id for foreign key
                power_delta=-baseline_power,  # Negative power loss
                kill_points_delta=0,  # KP doesn't change when player leaves
                dead_troops_delta=0,  # Dead troops don't change when player leaves
                kp_per_dead=0.0,
                player_left_kingdom=True,  # Mark as player who left
                is_zeroed=False,
                is_new_player=False
            )
            delta_stats.append(delta)

    print(f"Processed {players_processed} players for delta calculation")
    return delta_stats

def calculate_delta_stats_for_scan_pair(db: Session, start_scan_id: int, end_scan_id: int) -> None:
    """Calculate and store delta stats between two specific scans."""
    start_scan = crud.get_scan(db, scan_id=start_scan_id)
    end_scan = crud.get_scan(db, scan_id=end_scan_id)

    if not start_scan or not end_scan:
        raise HTTPException(status_code=404, detail="One or both scans not found")

    # Delete existing delta stats for this scan pair to avoid duplicates
    crud.delete_delta_stats_for_scan_pair(db, start_scan_id=start_scan_id, end_scan_id=end_scan_id)

    # Calculate new delta stats
    delta_stats = calculate_delta_stats(db, end_scan, start_scan)

    # Store delta stats in database
    for delta_stat in delta_stats:
        delta_stat_create = schemas.DeltaStatCreate(
            player_id=delta_stat.player_id,
            start_scan_id=delta_stat.start_scan_id,
            end_scan_id=delta_stat.end_scan_id,
            power_delta=delta_stat.power_delta,
            kill_points_delta=delta_stat.kill_points_delta,
            dead_troops_delta=delta_stat.dead_troops_delta,
            kp_per_dead=getattr(delta_stat, 'kp_per_dead', 0.0),
            is_zeroed=getattr(delta_stat, 'is_zeroed', False),
            meets_kp_target=getattr(delta_stat, 'meets_kp_target', False),
            meets_dead_target=getattr(delta_stat, 'meets_dead_target', False)
        )
        crud.create_delta_stat(db, delta_stat_create)

    print(f"Created {len(delta_stats)} delta stats between scan {start_scan_id} and {end_scan_id}")

def get_performance_report(db: Session, scan_id: Optional[int] = None) -> Dict:
    """Generate comprehensive performance report for KvK analysis."""
    if scan_id:
        scan = crud.get_scan(db, scan_id=scan_id)
        if not scan:
            raise HTTPException(status_code=404, detail="Scan not found")
    else:
        # Get the most recent scan
        scan = crud.get_latest_scan(db)
        if not scan:
            raise HTTPException(status_code=404, detail="No scans found")

    baseline_scan = crud.get_baseline_scan(db)
    if not baseline_scan:
        raise HTTPException(status_code=404, detail="No baseline scan found")

    # Get delta stats for this scan vs baseline
    delta_stats = crud.get_delta_stats_for_scan_pair(db, start_scan_id=baseline_scan.id, end_scan_id=scan.id)

    # Calculate summary statistics
    total_power_gain = sum(ds.power_delta for ds in delta_stats if ds.power_delta > 0)
    total_power_loss = sum(abs(ds.power_delta) for ds in delta_stats if ds.power_delta < 0)
    total_kp_gain = sum(ds.kill_points_delta for ds in delta_stats if ds.kill_points_delta > 0)
    total_dead_troops = sum(ds.dead_troops_delta for ds in delta_stats if ds.dead_troops_delta > 0)

    new_players = len([ds for ds in delta_stats if getattr(ds, 'is_new_player', False)])
    players_left = len([ds for ds in delta_stats if getattr(ds, 'player_left_kingdom', False)])

    # Top performers
    top_power_gainers = sorted(
        [ds for ds in delta_stats if ds.power_delta > 0],
        key=lambda x: x.power_delta,
        reverse=True
    )[:10]

    top_kp_gainers = sorted(
        [ds for ds in delta_stats if ds.kill_points_delta > 0],
        key=lambda x: x.kill_points_delta,
        reverse=True
    )[:10]

    return {
        "scan_info": {
            "scan_id": scan.id,
            "scan_name": scan.name,
            "scan_date": scan.created_at,
            "baseline_scan_id": baseline_scan.id,
            "baseline_scan_name": baseline_scan.name
        },
        "summary": {
            "total_power_gain": total_power_gain,
            "total_power_loss": total_power_loss,
            "net_power_change": total_power_gain - total_power_loss,
            "total_kill_points_gain": total_kp_gain,
            "total_dead_troops": total_dead_troops,
            "new_players": new_players,
            "players_left": players_left,
            "active_players": len(delta_stats)
        },
        "top_performers": {
            "power_gainers": [
                {
                    "player_name": ds.player.name if ds.player else "Unknown",
                    "power_gain": ds.power_delta,
                    "alliance": ds.player.alliance if ds.player else None
                }
                for ds in top_power_gainers
            ],
            "kill_point_gainers": [
                {
                    "player_name": ds.player.name if ds.player else "Unknown",
                    "kill_points_gain": ds.kill_points_delta,
                    "alliance": ds.player.alliance if ds.player else None
                }
                for ds in top_kp_gainers
            ]
        }
    }

# --- Excel Export Functions ---
def export_scan_to_excel(db: Session, scan_id: int) -> io.BytesIO:
    """Export scan data to Excel format."""
    scan = crud.get_scan(db, scan_id=scan_id)
    if not scan:
        raise HTTPException(status_code=404, detail="Scan not found")

    # Prepare data for export
    data = []
    for stat in scan.player_stats:
        player = stat.player
        data.append({
            "Player Name": player.name if player else "Unknown",
            "Governor ID": player.governor_id if player else None,
            "Alliance": player.alliance if player else None,
            "Power": stat.power,
            "Total Kill Points": stat.total_kill_points,
            "T1 Kills": stat.kill_points_t1,
            "T2 Kills": stat.kill_points_t2,
            "T3 Kills": stat.kill_points_t3,
            "T4 Kills": stat.kill_points_t4,
            "T5 Kills": stat.kill_points_t5,
            "T4+T5 Kills": stat.t45_kills,
            "Dead Troops": stat.dead_troops,
            "Ranged": stat.ranged,
            "RSS Gathered": stat.rss_gathered,
            "RSS Assistance": stat.rss_assistance,
            "Helps": stat.helps
        })

    # Create DataFrame and Excel file
    df = pd.DataFrame(data)

    # Create Excel file in memory
    output = io.BytesIO()
    with pd.ExcelWriter(output, engine='openpyxl') as writer:
        df.to_excel(writer, sheet_name=f"Scan_{scan.name}", index=False)

    output.seek(0)
    return output

def export_performance_report_to_excel(db: Session, scan_id: Optional[int] = None) -> io.BytesIO:
    """Export performance report to Excel format."""
    report = get_performance_report(db, scan_id)

    # Prepare data for different sheets
    summary_data = [{
        "Metric": key.replace("_", " ").title(),
        "Value": value
    } for key, value in report["summary"].items()]

    power_gainers_data = report["top_performers"]["power_gainers"]
    kp_gainers_data = report["top_performers"]["kill_point_gainers"]

    # Create DataFrames
    summary_df = pd.DataFrame(summary_data)
    power_df = pd.DataFrame(power_gainers_data)
    kp_df = pd.DataFrame(kp_gainers_data)

    # Create Excel file in memory
    output = io.BytesIO()
    with pd.ExcelWriter(output, engine='openpyxl') as writer:
        summary_df.to_excel(writer, sheet_name="Summary", index=False)
        power_df.to_excel(writer, sheet_name="Top Power Gainers", index=False)
        kp_df.to_excel(writer, sheet_name="Top KP Gainers", index=False)

    output.seek(0)
    return output

# --- Alliance Analysis Functions ---
def get_alliance_performance(db: Session, scan_id: Optional[int] = None) -> Dict:
    """Get performance breakdown by alliance."""
    if scan_id:
        scan = crud.get_scan(db, scan_id=scan_id)
        if not scan:
            raise HTTPException(status_code=404, detail="Scan not found")
    else:
        scan = crud.get_latest_scan(db)
        if not scan:
            raise HTTPException(status_code=404, detail="No scans found")

    baseline_scan = crud.get_baseline_scan(db)
    if not baseline_scan:
        raise HTTPException(status_code=404, detail="No baseline scan found")

    # Get delta stats
    delta_stats = crud.get_delta_stats_for_scan_pair(db, start_scan_id=baseline_scan.id, end_scan_id=scan.id)

    # Group by alliance
    alliance_stats = {}
    for ds in delta_stats:
        if ds.player and ds.player.alliance:
            alliance = ds.player.alliance
            if alliance not in alliance_stats:
                alliance_stats[alliance] = {
                    "member_count": 0,
                    "total_power_gain": 0,
                    "total_kp_gain": 0,
                    "total_dead_troops": 0,
                    "new_members": 0
                }

            alliance_stats[alliance]["member_count"] += 1
            alliance_stats[alliance]["total_power_gain"] += max(0, ds.power_delta)
            alliance_stats[alliance]["total_kp_gain"] += max(0, ds.kill_points_delta)
            alliance_stats[alliance]["total_dead_troops"] += max(0, ds.dead_troops_delta)

            if getattr(ds, 'is_new_player', False):
                alliance_stats[alliance]["new_members"] += 1

    # Calculate averages and sort by performance
    for alliance, stats in alliance_stats.items():
        if stats["member_count"] > 0:
            stats["avg_power_gain"] = stats["total_power_gain"] / stats["member_count"]
            stats["avg_kp_gain"] = stats["total_kp_gain"] / stats["member_count"]

    # Sort alliances by total power gain
    sorted_alliances = sorted(
        alliance_stats.items(),
        key=lambda x: x[1]["total_power_gain"],
        reverse=True
    )

    return {
        "scan_info": {
            "scan_id": scan.id,
            "scan_name": scan.name,
            "baseline_scan_id": baseline_scan.id
        },
        "alliance_performance": dict(sorted_alliances)
    }

# --- Utility Functions ---
def get_player_history(db: Session, player_id: int) -> Dict:
    """Get complete performance history for a specific player."""
    player = crud.get_player(db, player_id=player_id)
    if not player:
        raise HTTPException(status_code=404, detail="Player not found")

    # Get all stats for this player across all scans
    player_stats = crud.get_player_stats_history(db, player_id=player_id)

    # Get all delta stats for this player
    delta_stats = crud.get_player_delta_stats(db, player_id=player_id)

    return {
        "player_info": {
            "id": player.id,
            "name": player.name,
            "governor_id": player.governor_id,
            "alliance": player.alliance
        },
        "scan_history": [
            {
                "scan_id": stat.scan_id,
                "scan_name": stat.scan.name if stat.scan else "Unknown",
                "scan_date": stat.scan.created_at if stat.scan else None,
                "power": stat.power,
                "total_kill_points": stat.total_kill_points,
                "dead_troops": stat.dead_troops,
                "t45_kills": stat.t45_kills
            }
            for stat in player_stats
        ],
        "performance_deltas": [
            {
                "start_scan_id": ds.start_scan_id,
                "end_scan_id": ds.end_scan_id,
                "power_delta": ds.power_delta,
                "kill_points_delta": ds.kill_points_delta,
                "dead_troops_delta": ds.dead_troops_delta
            }
            for ds in delta_stats
        ]
    }

def cleanup_old_scans(db: Session, keep_count: int = 10) -> Dict:
    """Clean up old scans while preserving the baseline and most recent scans."""
    all_scans = crud.get_all_scans(db)
    baseline_scan = crud.get_baseline_scan(db)

    if len(all_scans) <= keep_count:
        return {"message": "No scans need to be cleaned up", "deleted_count": 0}

    # Sort scans by creation date (newest first)
    sorted_scans = sorted(all_scans, key=lambda x: x.created_at, reverse=True)

    # Keep the most recent scans and the baseline
    scans_to_keep = set()
    scans_to_keep.update([scan.id for scan in sorted_scans[:keep_count]])

    if baseline_scan:
        scans_to_keep.add(baseline_scan.id)

    # Delete old scans
    deleted_count = 0
    for scan in all_scans:
        if scan.id not in scans_to_keep:
            crud.delete_scan(db, scan_id=scan.id)
            deleted_count += 1

    return {
        "message": f"Cleaned up {deleted_count} old scans",
        "deleted_count": deleted_count,
        "remaining_scans": len(all_scans) - deleted_count
    }

# --- KvK Performance Analysis ---
def get_kvk_performance_summary(db: Session, kvk_id: int, limit: int = 50) -> Dict:
    """Get comprehensive KvK performance analysis with enhanced metrics."""
    try:
        # Get KvK information
        kvk = crud_kvk.get_kvk(db, kvk_id=kvk_id)
        if not kvk:
            raise HTTPException(status_code=404, detail="KvK not found")

        # Get all scans for this KvK
        kvk_scans = crud_kvk.get_kvk_scans(db, kvk_id=kvk_id)
        if not kvk_scans:
            raise HTTPException(status_code=404, detail="No scans found for this KvK")

        # Find baseline and latest scans
        baseline_scan = None
        latest_scan = None

        for scan in kvk_scans:
            if scan.is_baseline:
                baseline_scan = scan
            if not latest_scan or scan.timestamp > latest_scan.timestamp:
                latest_scan = scan

        if not baseline_scan:
            raise HTTPException(status_code=404, detail="No baseline scan found for this KvK")

        if not latest_scan:
            raise HTTPException(status_code=404, detail="No scans found for this KvK")

        # Check if we have only a baseline scan (no progress to show)
        if baseline_scan.id == latest_scan.id:
            # Only baseline scan exists, return baseline data with zero deltas
            baseline_stats = baseline_scan.player_stats
            performance_data = []

            for stat in baseline_stats:
                if not stat.player:
                    continue

                performance_data.append({
                    "player_id": stat.player.id,
                    "player_name": stat.player.name,
                    "alliance": stat.player.alliance,
                    "governor_id": stat.player.governor_id,
                    "current_power": stat.power,
                    "current_kp": stat.total_kill_points,
                    "current_dead": stat.dead_troops,
                    "current_t4_kills": stat.kill_points_t4 or 0,
                    "current_t5_kills": stat.kill_points_t5 or 0,
                    "current_t45_kills": (stat.kill_points_t4 or 0) + (stat.kill_points_t5 or 0),
                    "power_delta": 0,  # No change from baseline
                    "kp_delta": 0,     # No change from baseline
                    "dead_delta": 0,   # No change from baseline
                    "t4_delta": 0,     # No change from baseline
                    "t5_delta": 0,     # No change from baseline
                    "t45_delta": 0,    # No change from baseline
                    "kp_per_dead": 0.0,
                    "efficiency_score": 0.0,
                    "is_top_performer": False,
                    "needs_improvement": False,
                    "is_zeroed": stat.power == 0,
                    # Additional aliases for frontend compatibility
                    "kill_points_gain": 0,
                    "power_gain": 0,
                    "dead_troops_gain": 0,
                    "t45_kills_gain": 0
                })
        else:
            # Get delta stats between baseline and latest scan
            delta_stats = crud.get_delta_stats_for_scan_pair(db, start_scan_id=baseline_scan.id, end_scan_id=latest_scan.id)

            if not delta_stats:
                # If no delta stats exist, calculate them
                calculate_delta_stats_for_scan_pair(db, start_scan_id=baseline_scan.id, end_scan_id=latest_scan.id)
                delta_stats = crud.get_delta_stats_for_scan_pair(db, start_scan_id=baseline_scan.id, end_scan_id=latest_scan.id)

            # Calculate performance metrics for each player
            performance_data = []
            for ds in delta_stats:
                if not ds.player:
                    continue

                # Get current stats from latest scan
                current_stat = None
                for stat in latest_scan.player_stats:
                    if stat.player_id == ds.player_id:
                        current_stat = stat
                        break

                if not current_stat:
                    continue

                # Calculate T4-5 kills delta
                current_t4_kills = current_stat.kill_points_t4 or 0
                current_t5_kills = current_stat.kill_points_t5 or 0

                # Get baseline stats for this player
                baseline_stat = None
                for stat in baseline_scan.player_stats:
                    if stat.player_id == ds.player_id:
                        baseline_stat = stat
                        break

                baseline_t4_kills = baseline_stat.kill_points_t4 or 0 if baseline_stat else 0
                baseline_t5_kills = baseline_stat.kill_points_t5 or 0 if baseline_stat else 0

                # T4/T5 deltas - allow negative values to track losses properly
                t4_delta = current_t4_kills - baseline_t4_kills
                t5_delta = current_t5_kills - baseline_t5_kills
                t45_delta = t4_delta + t5_delta

                # Calculate efficiency metrics with proper error handling
                # Only calculate if both values are positive
                if ds.kill_points_delta > 0 and ds.dead_troops_delta > 0:
                    kp_per_dead = ds.kill_points_delta / ds.dead_troops_delta
                else:
                    kp_per_dead = 0.0

                # Efficiency score calculation with better logic
                efficiency_score = 0.0
                if ds.power_delta < 0 and ds.kill_points_delta > 0:
                    # Player lost power but gained KP - good efficiency
                    efficiency_score = ds.kill_points_delta / abs(ds.power_delta)
                elif ds.power_delta > 0 and ds.kill_points_delta > 0:
                    # Player gained both power and KP
                    efficiency_score = ds.kill_points_delta / ds.power_delta
                elif ds.power_delta == 0 and ds.kill_points_delta > 0:
                    # No power change but gained KP - very efficient
                    efficiency_score = float(ds.kill_points_delta) / 1_000_000  # Normalize to millions
                else:
                    # No KP gain or negative KP (data issue)
                    efficiency_score = 0.0

                # Performance flags with better logic
                is_new_player = getattr(ds, 'is_new_player', False)
                
                # Top performer: High KP gains with good efficiency
                is_top_performer = (
                    ds.kill_points_delta >= 100_000_000 and  # 100M+ KP gain
                    (kp_per_dead >= 1.0 or efficiency_score >= 100.0)  # Good efficiency
                )
                
                # Needs improvement: Low or no KP gains (excluding new players and zeroed players)
                needs_improvement = (
                    not is_new_player and
                    not ds.is_zeroed and
                    ds.kill_points_delta < 10_000_000  # Less than 10M KP gain
                )

                performance_data.append({
                    "player_id": ds.player.id,
                    "player_name": ds.player.name,
                    "alliance": ds.player.alliance,
                    "governor_id": ds.player.governor_id,
                    "current_power": current_stat.power,
                    "current_kp": current_stat.total_kill_points,
                    "current_dead": current_stat.dead_troops,
                    "current_t4_kills": current_t4_kills,
                    "current_t5_kills": current_t5_kills,
                    "current_t45_kills": (current_t4_kills + current_t5_kills),
                    "power_delta": ds.power_delta,
                    "kp_delta": ds.kill_points_delta,
                    "dead_delta": ds.dead_troops_delta,
                    "t4_delta": t4_delta,
                    "t5_delta": t5_delta,
                    "t45_delta": t45_delta,
                    "kp_per_dead": round(kp_per_dead, 2),
                    "efficiency_score": round(efficiency_score, 2),
                    "is_top_performer": is_top_performer,
                    "needs_improvement": needs_improvement,
                    "is_new_player": is_new_player,
                    "is_zeroed": ds.is_zeroed or False,
                    # Additional aliases for frontend compatibility
                    "kill_points_gain": ds.kill_points_delta,
                    "power_gain": ds.power_delta,
                    "dead_troops_gain": ds.dead_troops_delta,
                    "t45_kills_gain": t45_delta
                })

        # Sort by kill points delta (descending) and limit results
        performance_data.sort(key=lambda x: x["kp_delta"], reverse=True)
        limited_performance_data = performance_data[:limit]

        # Calculate summary statistics - Fixed to include power losses
        total_power_gain = sum(p["power_delta"] for p in performance_data if p["power_delta"] > 0)
        total_power_loss = sum(abs(p["power_delta"]) for p in performance_data if p["power_delta"] < 0)
        net_power_change = sum(p["power_delta"] for p in performance_data)
        total_kp_gain = sum(p["kp_delta"] for p in performance_data if p["kp_delta"] > 0)
        total_dead_troops = sum(p["dead_delta"] for p in performance_data if p["dead_delta"] > 0)

        avg_power_gain = total_power_gain / len(performance_data) if performance_data else 0
        avg_kp_gain = total_kp_gain / len(performance_data) if performance_data else 0
        avg_dead_troops = total_dead_troops / len(performance_data) if performance_data else 0

        top_performers_count = len([p for p in performance_data if p["is_top_performer"]])
        needs_improvement_count = len([p for p in performance_data if p["needs_improvement"]])
        power_loss_players = len([p for p in performance_data if p["power_delta"] < 0])

        return {
            "kvk_info": {
                "kvk_id": kvk.id,
                "kvk_name": kvk.name,
                "start_date": kvk.start_date,
                "end_date": kvk.end_date,
                "status": kvk.status
            },
            "scan_period": {
                "baseline_scan_id": baseline_scan.id,
                "baseline_scan_name": baseline_scan.name,
                "baseline_scan_date": baseline_scan.timestamp,
                "latest_scan_id": latest_scan.id,
                "latest_scan_name": latest_scan.name,
                "latest_scan_date": latest_scan.timestamp
            },
            "summary_stats": {
                "total_players": len(performance_data),
                "total_power_gain": total_power_gain,
                "total_power_loss": total_power_loss,
                "net_power_change": net_power_change,
                "total_kp_gain": total_kp_gain,
                "total_dead_troops": total_dead_troops,
                "avg_power_gain": round(avg_power_gain, 0),
                "avg_kp_gain": round(avg_kp_gain, 0),
                "avg_dead_troops": round(avg_dead_troops, 0),
                "top_performers": top_performers_count,
                "needs_improvement": needs_improvement_count,
                "power_loss_players": power_loss_players
            },
            "performance_data": limited_performance_data
        }

    except HTTPException:
        raise
    except Exception as e:
        import traceback
        error_details = traceback.format_exc()
        print(f"Performance summary error: {error_details}")
        raise HTTPException(status_code=500, detail=f"Error generating KvK performance summary: {str(e)}")

def get_kingdom_overview_summary(db: Session) -> Dict:
    """Get comprehensive kingdom overview with current stats and recent performance."""
    try:
        # Get latest scan for current stats
        latest_scan = crud.get_latest_scan(db)
        if not latest_scan:
            raise HTTPException(status_code=404, detail="No scans found")

        # Get baseline scan for performance calculations
        baseline_scan = crud.get_baseline_scan(db)

        # Current kingdom stats from latest scan
        current_stats = {
            "total_players": len(latest_scan.player_stats),
            "total_power": sum(stat.power for stat in latest_scan.player_stats if stat.power),
            "total_kill_points": sum(stat.total_kill_points for stat in latest_scan.player_stats if stat.total_kill_points),
            "total_dead_troops": sum(stat.dead_troops for stat in latest_scan.player_stats if stat.dead_troops),
            "avg_power": 0,
            "power_champion": None,
            "kp_champion": None,
            "scan_date": latest_scan.timestamp.isoformat() if latest_scan.timestamp else None
        }

        if current_stats["total_players"] > 0:
            current_stats["avg_power"] = current_stats["total_power"] / current_stats["total_players"]

        # Find champions (highest current stats)
        if latest_scan.player_stats:
            power_champion = max(latest_scan.player_stats, key=lambda x: x.power or 0)
            kp_champion = max(latest_scan.player_stats, key=lambda x: x.total_kill_points or 0)

            current_stats["power_champion"] = {
                "player_name": power_champion.player.name if power_champion.player else "Unknown",
                "player_id": power_champion.player_id,
                "power": power_champion.power,
                "alliance": power_champion.player.alliance if power_champion.player else None
            }

            current_stats["kp_champion"] = {
                "player_name": kp_champion.player.name if kp_champion.player else "Unknown",
                "player_id": kp_champion.player_id,
                "kill_points": kp_champion.total_kill_points,
                "alliance": kp_champion.player.alliance if kp_champion.player else None
            }

        # Performance data (if baseline exists)
        performance_data = None
        if baseline_scan and baseline_scan.id != latest_scan.id:
            performance_data = get_general_performance_summary(db, limit=1000)  # Get all players

        return {
            "kingdom_stats": current_stats,
            "performance_data": performance_data,
            "has_baseline": baseline_scan is not None,
            "baseline_scan_id": baseline_scan.id if baseline_scan else None,
            "latest_scan_id": latest_scan.id
        }

    except HTTPException:
        raise
    except Exception as e:
        import traceback
        error_details = traceback.format_exc()
        print(f"Kingdom overview error: {error_details}")
        raise HTTPException(status_code=500, detail=f"Error generating kingdom overview: {str(e)}")

def get_general_performance_summary(db: Session, limit: int = 50) -> Dict:
    """Get comprehensive performance analysis for general scans (not tied to specific KvK)."""
    try:
        # Get baseline and latest scans (general scans, not KvK-specific)
        baseline_scan = crud.get_baseline_scan(db)
        latest_scan = crud.get_latest_scan(db)

        if not baseline_scan:
            raise HTTPException(status_code=404, detail="No baseline scan found")

        if not latest_scan:
            raise HTTPException(status_code=404, detail="No scans found")

        if baseline_scan.id == latest_scan.id:
            raise HTTPException(status_code=400, detail="Baseline and latest scan are the same. Need at least 2 scans for performance analysis.")

        print(f"General performance analysis: baseline scan {baseline_scan.id} vs latest scan {latest_scan.id}")
        print(f"Baseline scan has {len(baseline_scan.player_stats) if baseline_scan.player_stats else 0} players")
        print(f"Latest scan has {len(latest_scan.player_stats) if latest_scan.player_stats else 0} players")

        # Get delta stats between baseline and latest scan
        delta_stats = crud.get_delta_stats_for_scan_pair(db, baseline_scan.id, latest_scan.id)

        if not delta_stats:
            raise HTTPException(status_code=404, detail="No performance data found between baseline and latest scan")

        # Build performance data for each player
        performance_data = []

        for delta_stat in delta_stats:
            player = delta_stat.player
            if not player:
                continue

            # Get current stats from latest scan
            current_stat = None
            for stat in latest_scan.player_stats:
                if stat.player_id == delta_stat.player_id:
                    current_stat = stat
                    break

            if not current_stat:
                continue

            # Calculate performance metrics
            kp_delta = delta_stat.kill_points_delta or 0
            power_delta = delta_stat.power_delta or 0
            dead_delta = delta_stat.dead_troops_delta or 0

            # Calculate efficiency metrics with proper validation
            if kp_delta > 0 and dead_delta > 0:
                kp_per_dead = kp_delta / dead_delta
            else:
                kp_per_dead = 0.0

            # Performance flags with improved logic
            is_new_player = getattr(delta_stat, 'is_new_player', False)
            player_left = getattr(delta_stat, 'player_left_kingdom', False)
            
            # Top performer: High KP gains
            is_top_performer = kp_delta >= 100_000_000  # 100M+ KP gain
            
            # Needs improvement: Low or no KP gains (excluding new players)
            needs_improvement = (
                not is_new_player and
                not player_left and
                kp_delta < 10_000_000  # Less than 10M KP gain
            )

            # Calculate T4-5 kills delta
            current_t4_kills = current_stat.kill_points_t4 or 0
            current_t5_kills = current_stat.kill_points_t5 or 0

            # Get baseline stats for this player
            baseline_stat = None
            for stat in baseline_scan.player_stats:
                if stat.player_id == delta_stat.player_id:
                    baseline_stat = stat
                    break

            baseline_t4_kills = baseline_stat.kill_points_t4 or 0 if baseline_stat else 0
            baseline_t5_kills = baseline_stat.kill_points_t5 or 0 if baseline_stat else 0

            # T4/T5 deltas - allow negative values to track losses properly
            t4_delta = current_t4_kills - baseline_t4_kills
            t5_delta = current_t5_kills - baseline_t5_kills
            t45_delta = t4_delta + t5_delta

            performance_data.append({
                "player_id": player.id,
                "player_name": player.name,
                "governor_id": player.governor_id,
                "alliance": player.alliance,
                "current_power": current_stat.power,
                "current_kp": current_stat.total_kill_points,
                "current_dead": current_stat.dead_troops,
                "current_t4_kills": current_t4_kills,
                "current_t5_kills": current_t5_kills,
                "power_delta": power_delta,
                "kp_delta": kp_delta,
                "dead_delta": dead_delta,
                "t4_delta": t4_delta,
                "t5_delta": t5_delta,
                "t45_delta": t45_delta,
                "kp_per_dead": round(kp_per_dead, 2) if kp_per_dead > 0 else 0,
                "is_top_performer": is_top_performer,
                "needs_improvement": needs_improvement,
                "is_new_player": is_new_player,
                "player_left_kingdom": player_left,
                "kill_points_gain": kp_delta,  # Alias for frontend compatibility
                "power_gain": power_delta,
                "dead_troops_gain": dead_delta,
                "t45_kills_gain": t45_delta  # T4+T5 kills gained
            })

        # Sort by kill points delta (descending)
        performance_data.sort(key=lambda x: x["kp_delta"], reverse=True)

        # Calculate summary statistics - Fixed to include power losses
        total_power_gain = sum(p["power_delta"] for p in performance_data if p["power_delta"] > 0)
        total_power_loss = sum(abs(p["power_delta"]) for p in performance_data if p["power_delta"] < 0)
        net_power_change = sum(p["power_delta"] for p in performance_data)
        total_kp_gain = sum(p["kp_delta"] for p in performance_data if p["kp_delta"] > 0)
        total_dead_troops = sum(p["dead_delta"] for p in performance_data if p["dead_delta"] > 0)

        avg_power_gain = total_power_gain / len(performance_data) if performance_data else 0
        avg_kp_gain = total_kp_gain / len(performance_data) if performance_data else 0
        avg_dead_troops = total_dead_troops / len(performance_data) if performance_data else 0

        top_performers_count = len([p for p in performance_data if p["is_top_performer"]])
        needs_improvement_count = len([p for p in performance_data if p["needs_improvement"]])
        new_players_count = len([p for p in performance_data if p["is_new_player"]])
        players_left_count = len([p for p in performance_data if p["player_left_kingdom"]])

        return {
            "scan_period": {
                "baseline_scan_id": baseline_scan.id,
                "baseline_scan_name": baseline_scan.name,
                "baseline_scan_date": baseline_scan.timestamp.isoformat() if baseline_scan.timestamp else None,
                "latest_scan_id": latest_scan.id,
                "latest_scan_name": latest_scan.name,
                "latest_scan_date": latest_scan.timestamp.isoformat() if latest_scan.timestamp else None
            },
            "summary_stats": {
                "total_players": len(performance_data),
                "total_power_gain": total_power_gain,
                "total_power_loss": total_power_loss,
                "net_power_change": net_power_change,
                "total_kp_gain": total_kp_gain,
                "total_dead_troops": total_dead_troops,
                "avg_power_gain": round(avg_power_gain, 0),
                "avg_kp_gain": round(avg_kp_gain, 0),
                "avg_dead_troops": round(avg_dead_troops, 0),
                "top_performers": top_performers_count,
                "needs_improvement": needs_improvement_count,
                "new_players": new_players_count,
                "players_left": players_left_count
            },
            "performance_data": performance_data  # Return ALL players, not limited
        }

    except HTTPException:
        raise
    except Exception as e:
        import traceback
        error_details = traceback.format_exc()
        print(f"General performance summary error: {error_details}")
        raise HTTPException(status_code=500, detail=f"Error generating general performance summary: {str(e)}")
