"""
Simple test to verify calculation fixes work.
"""

import sys
import os

# Add the parent directory to the path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

try:
    print("Testing imports...")
    from backend.database import SessionLocal
    from backend import crud, services
    print("✅ All imports successful")
    
    print("\nTesting database connection...")
    db = SessionLocal()
    
    # Test basic CRUD functions
    print("Testing CRUD functions...")
    latest_scan = crud.get_latest_scan(db)
    baseline_scan = crud.get_baseline_scan(db)
    
    if latest_scan:
        print(f"✅ Latest scan found: {latest_scan.name}")
    else:
        print("❌ No latest scan found")
        
    if baseline_scan:
        print(f"✅ Baseline scan found: {baseline_scan.name}")
    else:
        print("❌ No baseline scan found")
    
    # Test KvK functions
    print("\nTesting KvK functions...")
    kvks = crud.get_kvks(db)
    print(f"✅ Found {len(kvks)} KvKs")
    
    # Test dashboard data
    print("\nTesting dashboard data...")
    dashboard_data = crud.get_dashboard_data(db)
    print(f"✅ Dashboard data retrieved: {type(dashboard_data)}")
    
    # Test delta calculations if we have scans
    if latest_scan and baseline_scan and latest_scan.id != baseline_scan.id:
        print("\nTesting delta calculations...")
        delta_stats = services.calculate_delta_stats(db, latest_scan, baseline_scan)
        print(f"✅ Calculated {len(delta_stats)} delta stats")
        
        # Count negative KP deltas (should be warnings, not errors)
        negative_kp_count = sum(1 for ds in delta_stats if ds.kill_points_delta < 0)
        print(f"⚠️  Found {negative_kp_count} negative KP deltas (data quality warnings)")
        
        # Test performance functions
        print("\nTesting performance functions...")
        try:
            performance_data = services.get_general_performance_summary(db, limit=10)
            print(f"✅ General performance data retrieved: {len(performance_data.get('performance_data', []))} players")
        except Exception as e:
            print(f"❌ Error getting performance data: {e}")
    
    db.close()
    print("\n🎉 All basic tests passed!")
    
except Exception as e:
    print(f"❌ Error: {e}")
    import traceback
    traceback.print_exc()
