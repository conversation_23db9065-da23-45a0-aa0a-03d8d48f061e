# Calculation Fixes Summary

## Date: 2025-05-27

### Issues Fixed

#### 1. **Backend Calculation Issues**

##### Delta Calculation Logic (backend/services.py)
- **Fixed negative KP delta handling**: Previously set negative KP deltas to 0, now preserves actual values for investigation
- **Improved KP per dead calculation**: Only calculates when both KP and dead troops are positive
- **Enhanced efficiency score calculation**: Better logic for different scenarios:
  - Power loss with KP gain: `kp_delta / abs(power_delta)`
  - Power gain with KP gain: `kp_delta / power_delta`
  - No power change with KP gain: `kp_delta / 1,000,000` (normalized)
  - Other cases: 0.0

##### Performance Flags (backend/services.py)
- **Updated top performer criteria**: 
  - Changed from 500M+ KP to 100M+ KP
  - Added efficiency requirement (KP per dead >= 1.0 or efficiency score >= 100.0)
- **Improved underperforming logic**:
  - Changed from "zero KP delta" to "less than 10M KP gain"
  - Excludes new players and zeroed players
  - More realistic threshold for identifying players who need improvement

#### 2. **Frontend Display Issues**

##### KvKPlayerTable.tsx
- **Fixed power display**: Now properly shows red color for negative power values
- **Added negative KP handling**: Shows red color for negative KP values (data integrity issues)
- **Improved color coding**: Better visual feedback for gains/losses

##### KvKPerformanceDashboard.tsx
- **Updated underperforming filter**: Changed from "zero KP" to "less than 10M KP"
- **Improved sorting**: Underperforming players now sorted by lowest KP first
- **Fixed player count**: Correctly counts underperforming players

### Data Integrity Checks Added

1. **Negative KP Detection**: System now logs warnings but preserves negative values for investigation
2. **T4/T5 Calculation Validation**: Allows negative values (for data corrections) but tracks them
3. **Efficiency Metrics**: Only calculated when valid data is present

### Testing

Created comprehensive test script (`backend/test_all_calculations.py`) that verifies:
- Excel data integrity
- Delta calculations
- KvK performance calculations
- Display data mapping

### Next Steps

1. **Restart the backend server** to apply the calculation fixes
2. **Restart the frontend server** to apply the display fixes
3. **Run the test script** to verify all calculations:
   ```bash
   cd backend
   python test_all_calculations.py
   ```

### Important Notes

- The system now preserves negative KP deltas instead of setting them to 0, which helps identify data issues
- Underperforming threshold changed from 0 KP to less than 10M KP gain
- Top performer threshold reduced from 500M to 100M KP gain with efficiency requirements
- All calculations now handle edge cases (division by zero, null values, etc.)

### Potential Data Issues to Investigate

If you see negative KP deltas in the logs, it could indicate:
1. Data entry errors in the Excel file
2. Player data was reset or corrected between scans
3. Different players with the same governor ID
4. Excel formula errors

These should be investigated on a case-by-case basis by checking the source Excel files.
